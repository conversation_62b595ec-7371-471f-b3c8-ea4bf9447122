import React, { PureComponent } from 'react';
import { userOptions } from './options';
import { ScrollBoard } from '@jiaminghi/data-view-react';

class UserSituation extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      config: {
        // 表头背景色
        headerBGC: '#443dc5',
        // 奇数行背景色
        oddRowBGC: '#09184F',
        // 偶数行背景色
        evenRowBGC: '#070C34',
        // 行号
        index: true,
        // 行号表头
        indexHeader: '序号',
        // 宽度 - 调整比例：序号50, 问题120, 答案180, 时间80
        columnWidth: [50, 120, 180, 80],
        // 对其方式
        align: ['center', 'left', 'left', 'center'],
        // 表行数 - 显示的行数，超出部分会滚动
        rowNum: 10,
        // 滚动等待时间(ms)
        waitTime: 2000,
        // 自定义单元格样式
        cellStyle: {
          fontSize: '12px',
          color: '#fff',
          textAlign: 'left',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        },
        // 悬浮提示
        hoverPause: true
      },
    };
  }

  render() {
    const { userSitua } = this.props;

    // 构建 ScrollBoard 的配置
    const hasData = userSitua && userSitua.data && userSitua.data.length > 0;
    const config = {
      ...this.state.config,
      ...userOptions(userSitua),
      hoverPause: hasData, // 根据是否有数据动态设置
    };

    return (
      <div>
        {hasData ? (
          <ScrollBoard
            config={config}
            style={{
              width: '5.475rem',
              height: '6.875rem',
            }}
          />
        ) : (
          <div style={{ width: '5.475rem', height: '6.875rem', overflow: 'hidden' }}>
            {/* 表头 */}
            <div style={{
              display: 'flex',
              backgroundColor: '#443dc5',
              color: '#fff',
              fontSize: '12px',
              fontWeight: 'bold',
              height: '35px',
              lineHeight: '30px'
            }}>
              <div style={{ width: '50px', textAlign: 'center', borderRight: '1px solid #333' }}>序号</div>
              <div style={{ width: '120px', textAlign: 'center', borderRight: '1px solid #333', paddingLeft: '8px' }}>问题</div>
              <div style={{ width: '180px', textAlign: 'center', borderRight: '1px solid #333', paddingLeft: '8px' }}>答案</div>
              <div style={{ width: '100px', textAlign: 'center', paddingLeft: '8px' }}>时间</div>
            </div>
            {/* 无数据提示 */}
            <div style={{
              height: 'calc(100% - 35px)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#BCDCFF',
              fontSize: '14px'
            }}>
              暂无数据
            </div>
          </div>
        )}
      </div>
    );
  }
}

export default UserSituation;
