(function(win, lib) {
  var doc = win.document;
  var docEl = doc.documentElement;
  var metaEl = doc.querySelector('meta[name="viewport"]');
  var flexibleEl = doc.querySelector('meta[name="flexible"]');
  var dpr = 0;
  var scale = 0;
  var tid;
  var flexible = lib.flexible || (lib.flexible = {});

  if (metaEl) {
    console.warn("将根据已有的meta标签来设置缩放比例");
    var match = metaEl
      .getAttribute("content")
      // eslint-disable-next-line no-useless-escape
      .match(/initial\-scale=([\d\.]+)/);
    if (match) {
      scale = parseFloat(match[1]);
      dpr = parseInt(1 / scale, 10);
    }
  } else if (flexibleEl) {
    var content = flexibleEl.getAttribute("content");
    if (content) {
      // eslint-disable-next-line no-useless-escape
      var initialDpr = content.match(/initial\-dpr=([\d\.]+)/);
      // eslint-disable-next-line no-useless-escape
      var maximumDpr = content.match(/maximum\-dpr=([\d\.]+)/);
      if (initialDpr) {
        dpr = parseFloat(initialDpr[1]);
        scale = parseFloat((1 / dpr).toFixed(2));
      }
      if (maximumDpr) {
        dpr = parseFloat(maximumDpr[1]);
        scale = parseFloat((1 / dpr).toFixed(2));
      }
    }
  }

  if (!dpr && !scale) {
    // eslint-disable-next-line no-unused-vars
    var isAndroid = win.navigator.appVersion.match(/android/gi);
    var isIPhone = win.navigator.appVersion.match(/iphone/gi);
    var devicePixelRatio = win.devicePixelRatio;
    if (isIPhone) {
      // iOS下，对于2和3的屏，用2倍的方案，其余的用1倍方案
      if (devicePixelRatio >= 3 && (!dpr || dpr >= 3)) {
        dpr = 3;
      } else if (devicePixelRatio >= 2 && (!dpr || dpr >= 2)) {
        dpr = 2;
      } else {
        dpr = 1;
      }
    } else {
      // 其他设备下，仍旧使用1倍的方案
      dpr = 1;
    }
    scale = 1 / dpr;
  }

  docEl.setAttribute("data-dpr", dpr);
  if (!metaEl) {
    metaEl = doc.createElement("meta");
    metaEl.setAttribute("name", "viewport");
    metaEl.setAttribute(
      "content",
      "initial-scale=" +
        scale +
        ", maximum-scale=" +
        scale +
        ", minimum-scale=" +
        scale +
        ", user-scalable=no"
    );
    if (docEl.firstElementChild) {
      docEl.firstElementChild.appendChild(metaEl);
    } else {
      var wrap = doc.createElement("div");
      wrap.appendChild(metaEl);
      doc.write(wrap.innerHTML);
    }
  }

  function refreshRem() {
    var width = docEl.getBoundingClientRect().width;
    var actualWidth = width / dpr;

    // 检测屏幕类型
    var isLaptop = (dpr > 1.1 && actualWidth < 1600) ||
      (actualWidth <= 1366 && dpr >= 1.25);
    var isExternalMonitor = dpr === 1.0 && actualWidth >= 1920;

    var minWidth, maxWidth, divisions, remMin, remMax;

    if (isLaptop) {
      // 笔记本屏幕适配 - 更宽松的限制
      minWidth = 1000;
      maxWidth = 2560;
      divisions = Math.max(18, Math.floor(24 * actualWidth / 1920));
      remMin = 40;
      remMax = 100;
    } else if (isExternalMonitor) {
      // 外接显示器适配 - 标准适配
      minWidth = 1366;
      maxWidth = 2560;
      divisions = 24;
      remMin = 60;
      remMax = 120;
    } else {
      // 默认适配
      minWidth = 1200;
      maxWidth = 2560;
      divisions = Math.max(20, Math.floor(24 * actualWidth / 1920));
      remMin = 50;
      remMax = 120;
    }

    // 应用宽度限制
    if (actualWidth < minWidth) {
      width = minWidth * dpr;
    } else if (actualWidth > maxWidth) {
      width = maxWidth * dpr;
    }

    var rem = width / divisions;

    // 确保rem值在合理范围内
    rem = Math.max(remMin, Math.min(rem, remMax));

    docEl.style.fontSize = rem + "px";
    flexible.rem = win.rem = rem;

    // 添加屏幕类型标识
    docEl.setAttribute("data-screen-type",
      isLaptop ? "laptop" : (isExternalMonitor ? "external" : "default"));

    // 添加调试信息
    if (typeof console !== 'undefined' && console.log) {
      console.log('Screen adaptation:', {
        actualWidth: actualWidth,
        dpr: dpr,
        rem: rem,
        divisions: divisions,
        screenType: isLaptop ? "laptop" : (isExternalMonitor ? "external" : "default")
      });
    }
  }

  win.addEventListener(
    "resize",
    function() {
      clearTimeout(tid);
      tid = setTimeout(refreshRem, 300);
    },
    false
  );
  win.addEventListener(
    "pageshow",
    function(e) {
      if (e.persisted) {
        clearTimeout(tid);
        tid = setTimeout(refreshRem, 300);
      }
    },
    false
  );

  if (doc.readyState === "complete") {
    doc.body.style.fontSize = 12 * dpr + "px";
  } else {
    doc.addEventListener(
      "DOMContentLoaded",
      // eslint-disable-next-line no-unused-vars
      function(e) {
        doc.body.style.fontSize = 12 * dpr + "px";
      },
      false
    );
  }

  refreshRem();

  flexible.dpr = win.dpr = dpr;
  flexible.refreshRem = refreshRem;
  flexible.rem2px = function(d) {
    var val = parseFloat(d) * this.rem;
    if (typeof d === "string" && d.match(/rem$/)) {
      val += "px";
    }
    return val;
  };
  flexible.px2rem = function(d) {
    var val = parseFloat(d) / this.rem;
    if (typeof d === "string" && d.match(/px$/)) {
      val += "rem";
    }
    return val;
  };
})(window, window["lib"] || (window["lib"] = {}));
