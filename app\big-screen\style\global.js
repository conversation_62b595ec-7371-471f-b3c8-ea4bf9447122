import { createGlobalStyle } from 'styled-components';

export const Globalstyle = createGlobalStyle`
  html, body, div, span, applet, object, iframe,
  h1, h2, h3, h4, h5, h6, p, blockquote, pre,
  a, abbr, acronym, address, big, cite, code,
  del, dfn, em, img, ins, kbd, q, s, samp,
  small, strike, strong, sub, sup, tt, var,
  b, u, i, center,
  dl, dt, dd, ol, ul, li,
  fieldset, form, label, legend,
  table, caption, tbody, tfoot, thead, tr, th, td,
  article, aside, canvas, details, embed, 
  figure, figcaption, footer, header, hgroup, 
  menu, nav, output, ruby, section, summary,
  time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
    box-sizing: border-box;
  }
  article, aside, details, figcaption, figure, 
  footer, header, hgroup, menu, nav, section {
    display: block;
  }
  body {
    line-height: 1;
  }
  ol, ul {
    list-style: none;
  }
  blockquote, q {
    quotes: none;
  }
  blockquote:before, blockquote:after,
  q:before, q:after {
    content: '';
    content: none;
  }
  table {
    border-collapse: collapse;
    border-spacing: 0;
  }

  .textOverHandle{
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  // 自定义模块
  .default-chart{
    width: 100%;
    height: 100%;
  }

  // 针对不同DPR的适配
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* 高DPR屏幕的特殊处理 */
    .detail-list-item {
      border-width: 0.5px !important;
    }
  }

  @media (-webkit-min-device-pixel-ratio: 3), (min-resolution: 288dpi) {
    /* 超高DPR屏幕的特殊处理 */
    .detail-list-item {
      border-width: 0.33px !important;
    }
  }

  // 确保在小屏幕上内容不会被截断
  @media (max-width: 1400px) {
    body {
      font-size: 14px !important;
    }
  }

  @media (max-width: 1200px) {
    body {
      font-size: 12px !important;
    }
  }

  // 针对不同屏幕类型的特殊样式
  html[data-screen-type="laptop"] {
    .detail-list-item {
      min-height: 1.2rem !important;
      padding: 0.1rem !important;
    }

    .detail-item-text h3 {
      font-size: 0.18rem !important;
      margin-bottom: 0.1rem !important;
    }

    .detail-item-text span {
      font-size: 0.22rem !important;
    }

    .feedback-box-item {
      min-height: 1.5rem !important;
    }

    .dis-text {
      font-size: 0.18rem !important;
    }
  }

  html[data-screen-type="external"] {
    .detail-list-item {
      min-height: 1.8rem !important;
      padding: 0.15rem !important;
    }

    .detail-item-text h3 {
      font-size: 0.22rem !important;
    }

    .detail-item-text span {
      font-size: 0.28rem !important;
    }

    .feedback-box-item {
      min-height: 2rem !important;
    }

    .dis-text {
      font-size: 0.22rem !important;
    }
  }

  // 通用的底部区域适配
  .center-bottom, .right-bottom-borderBox13 {
    min-height: auto !important;
    height: auto !important;
  }

  // 确保地图容器在所有状态下保持稳定
  .center-page > div:first-child {
    flex-shrink: 0 !important;
    min-height: 8.125rem !important;
    width: 10.625rem !important;
  }

  // 防止布局抖动的通用样式
  .detail-list {
    transition: none !important; /* 禁用过渡动画防止抖动 */
  }

  .detail-list-item {
    transition: none !important;
  }

  // 确保在地图加载时底部区域位置稳定
  .center-bottom {
    position: relative !important;
    z-index: 1 !important;
  }
`;
