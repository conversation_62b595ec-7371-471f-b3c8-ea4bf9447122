/**
 * 屏幕适配工具
 * 用于处理数据大屏在不同屏幕尺寸和DPR下的显示问题
 */

class ScreenAdapter {
  constructor() {
    this.init();
  }

  init() {
    this.detectScreen();
    this.bindEvents();
  }

  // 检测屏幕信息
  detectScreen() {
    const screen = window.screen;
    const dpr = window.devicePixelRatio || 1;
    const width = window.innerWidth;
    const height = window.innerHeight;

    this.screenInfo = {
      width,
      height,
      dpr,
      screenWidth: screen.width,
      screenHeight: screen.height,
      isLaptop: this.isLaptopScreen(width, height, dpr),
      isExternalMonitor: this.isExternalMonitor(width, height, dpr)
    };

    return this.screenInfo;
  }

  // 判断是否为笔记本屏幕
  isLaptopScreen(width, height, dpr) {
    // 笔记本屏幕通常具有以下特征：
    // 1. 较高的DPR (1.25, 1.5, 2.0等)
    // 2. 相对较小的物理尺寸
    // 3. 常见分辨率范围
    const physicalWidth = width * dpr;
    const physicalHeight = height * dpr;

    // 常见笔记本分辨率
    const laptopResolutions = [
      { w: 1366, h: 768 },
      { w: 1920, h: 1080 },
      { w: 2560, h: 1440 },
      { w: 2880, h: 1800 },
      { w: 3840, h: 2160 }
    ];

    const isCommonLaptopResolution = laptopResolutions.some(res =>
      Math.abs(physicalWidth - res.w) < 100 && Math.abs(physicalHeight - res.h) < 100
    );

    return (dpr > 1.1 && width < 1600) || isCommonLaptopResolution;
  }

  // 判断是否为外接显示器
  isExternalMonitor(width, height, dpr) {
    // 外接显示器通常具有以下特征：
    // 1. DPR通常为1.0
    // 2. 较大的物理尺寸
    // 3. 标准分辨率
    return dpr === 1.0 && width >= 1920;
  }

  // 获取适配参数
  getAdaptationParams() {
    const { width, height, dpr, isLaptop, isExternalMonitor } = this.screenInfo;

    let params = {
      minWidth: 1200,
      maxWidth: 2560,
      baseDivisions: 24,
      remRange: { min: 50, max: 120 },
      scaleFactor: 1
    };

    if (isLaptop) {
      // 笔记本屏幕适配
      params.minWidth = 1000;
      params.baseDivisions = Math.max(18, Math.floor(24 * width / 1920));
      params.scaleFactor = Math.min(1, width / 1366);
      params.remRange.min = 40;
      params.remRange.max = 100;
    } else if (isExternalMonitor) {
      // 外接显示器适配
      params.minWidth = 1366;
      params.baseDivisions = 24;
      params.scaleFactor = 1;
      params.remRange.min = 60;
      params.remRange.max = 120;
    }

    return params;
  }

  // 应用适配
  applyAdaptation() {
    const params = this.getAdaptationParams();
    const { width, dpr } = this.screenInfo;

    let actualWidth = width;

    // 应用最小/最大宽度限制
    if (width < params.minWidth) {
      actualWidth = params.minWidth;
    } else if (width > params.maxWidth) {
      actualWidth = params.maxWidth;
    }

    // 计算rem值
    const rem = Math.max(
      params.remRange.min,
      Math.min(
        (actualWidth * dpr) / params.baseDivisions,
        params.remRange.max
      )
    );

    // 应用到DOM
    document.documentElement.style.fontSize = rem + 'px';

    // 添加屏幕类型类名
    document.body.classList.remove('laptop-screen', 'external-monitor');
    if (this.screenInfo.isLaptop) {
      document.body.classList.add('laptop-screen');
    } else if (this.screenInfo.isExternalMonitor) {
      document.body.classList.add('external-monitor');
    }

    return { rem, params };
  }

  // 绑定事件
  bindEvents() {
    let resizeTimer;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(() => {
        this.detectScreen();
        this.applyAdaptation();
      }, 300);
    });

    // 监听屏幕变化（如连接/断开外接显示器）
    if (window.screen && window.screen.addEventListener) {
      window.screen.addEventListener('change', () => {
        setTimeout(() => {
          this.detectScreen();
          this.applyAdaptation();
        }, 500);
      });
    }
  }

  // 获取当前屏幕信息
  getScreenInfo() {
    return this.screenInfo;
  }
}

// 创建全局实例
const screenAdapter = new ScreenAdapter();

// 导出
export default screenAdapter;

// 兼容性导出
if (typeof window !== 'undefined') {
  window.screenAdapter = screenAdapter;
}
