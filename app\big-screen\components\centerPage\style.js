import styled from 'styled-components';

export const CenterPage = styled.div`
  margin-top: 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: calc(100vh - 120px); /* 确保最小高度 */
  height: auto;

  /* 地图容器样式 */
  > div:first-child {
    flex-shrink: 0; /* 防止地图区域被压缩 */
    min-height: 8.125rem; /* 确保地图区域最小高度 */
    width: 10.625rem;
    margin-bottom: 0.5rem; /* 与底部区域的间距 */
  }
`;

export const CenterBottom = styled.div`
  display: flex;
  margin-bottom: 0.25rem;
  margin-top: 0.5rem; /* 减少顶部间距，因为地图容器已有间距 */
  width: 100%;
  min-height: 3.25rem;
  height: auto; /* 改为自适应高度 */
  flex-shrink: 0; /* 防止底部区域被压缩 */
  position: relative; /* 确保定位稳定 */

  .detail-list {
    display: flex;
    flex-wrap: wrap;
    align-items: stretch; /* 改为拉伸对齐 */
    align-content: flex-start; /* 改为顶部对齐 */
    justify-content: space-around;
    width: 100%;
    gap: 0.125rem; /* 添加间距 */

    &-item {
      display: flex;
      align-items: center;
      position: relative;
      min-height: 1.5625rem;
      height: auto; /* 改为自适应高度 */
      padding: 0.125rem;
      width: calc(32% - 0.125rem); /* 考虑gap的宽度 */
      border-radius: 5px;
      border: 1px solid #343f4b;
      background-color: rgba(19, 25, 47, 0.8);
      box-sizing: border-box;

      /* 响应式适配 */
      @media (max-width: 1400px) {
        width: calc(48% - 0.125rem); /* 小屏幕时改为两列 */
        margin-bottom: 0.125rem;
      }

      @media (max-width: 1000px) {
        width: 100%; /* 更小屏幕时改为单列 */
        margin-bottom: 0.125rem;
      }

      img {
        width: 1.25rem;
        height: 1.25rem;
        flex-shrink: 0; /* 防止图片被压缩 */
      }
      .detail-item-text {
        margin-left: 0.125rem;
        flex: 1;
        min-width: 0; /* 允许文本收缩 */

        h3 {
          color: #bcdcff;
          font-size: 0.2rem; /* 使用rem单位保持一致性 */
          margin-bottom: 0.125rem;
          line-height: 1.2;
          word-break: break-word; /* 长文本换行 */
        }
        span {
          font-weight: 500;
          font-size: 0.25rem;
          font-weight: bolder;
          background: linear-gradient(to bottom, #fff, #4db6e5);
          color: transparent;
          -webkit-background-clip: text;
          background-clip: text;
          line-height: 1.2;
        }
        .unit {
          font-size: 0.2rem;
          margin-left: 0.125rem;
        }
      }
    }
  }
`;
